"""
Configuration settings for EntryAlert application.
"""
import os
from typing import List, Optional
from pydantic import BaseSettings, validator


class Settings(BaseSettings):
    """Application settings."""
    
    # Application
    APP_NAME: str = "EntryAlert"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    ENVIRONMENT: str = "development"
    
    # Database
    DATABASE_URL: str
    TEST_DATABASE_URL: Optional[str] = None
    
    # Redis
    REDIS_URL: str = "redis://localhost:6379"
    
    # Security
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # API Keys
    ALPHA_VANTAGE_API_KEY: Optional[str] = None
    YAHOO_FINANCE_API_KEY: Optional[str] = None
    IEX_CLOUD_API_KEY: Optional[str] = None
    
    # Email Configuration
    SMTP_HOST: str = "smtp.gmail.com"
    SMTP_PORT: int = 587
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    FROM_EMAIL: str = "<EMAIL>"
    
    # CORS
    ALLOWED_ORIGINS: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]
    
    @validator("ALLOWED_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = 100
    
    # Background Tasks
    SCREENING_INTERVAL_MINUTES: int = 5
    MARKET_DATA_UPDATE_INTERVAL_MINUTES: int = 1
    ALERT_CHECK_INTERVAL_MINUTES: int = 1
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"
    
    # WebSocket
    WS_HEARTBEAT_INTERVAL: int = 30
    
    # Cache
    CACHE_TTL_SECONDS: int = 300
    MARKET_DATA_CACHE_TTL: int = 60
    
    # Pagination
    DEFAULT_PAGE_SIZE: int = 50
    MAX_PAGE_SIZE: int = 1000
    
    # Stock Screening
    MAX_STOCKS_PER_SCREEN: int = 8000
    SCREENING_TIMEOUT_SECONDS: int = 30
    
    # Notifications
    MAX_ALERTS_PER_USER_PER_DAY: int = 100
    NOTIFICATION_BATCH_SIZE: int = 50
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()


# API URLs for different market data providers
MARKET_DATA_APIS = {
    "alpha_vantage": {
        "base_url": "https://www.alphavantage.co/query",
        "rate_limit": 5,  # requests per minute for free tier
    },
    "yahoo_finance": {
        "base_url": "https://query1.finance.yahoo.com/v8/finance/chart",
        "rate_limit": 2000,  # requests per hour
    },
    "iex_cloud": {
        "base_url": "https://cloud.iexapis.com/stable",
        "rate_limit": 100,  # requests per second for free tier
    }
}

# Stock market sectors (GICS)
STOCK_SECTORS = {
    "10": "Energy",
    "15": "Materials", 
    "20": "Industrials",
    "25": "Consumer Discretionary",
    "30": "Consumer Staples",
    "35": "Health Care",
    "40": "Financials",
    "45": "Information Technology",
    "50": "Communication Services",
    "55": "Utilities",
    "60": "Real Estate"
}

# Technical indicator parameters
TECHNICAL_INDICATORS = {
    "rsi": {"period": 14, "overbought": 70, "oversold": 30},
    "macd": {"fast": 12, "slow": 26, "signal": 9},
    "bollinger_bands": {"period": 20, "std_dev": 2},
    "moving_averages": {"short": 20, "medium": 50, "long": 200},
    "volume_sma": {"period": 20}
}

# Screening criteria defaults
DEFAULT_SCREENING_CRITERIA = {
    "min_price": 1.0,
    "max_price": 1000.0,
    "min_volume": 100000,
    "min_market_cap": 100000000,  # $100M
    "max_market_cap": 1000000000000,  # $1T
    "sectors": list(STOCK_SECTORS.keys()),
    "technical_filters": {
        "rsi_min": 0,
        "rsi_max": 100,
        "volume_surge_threshold": 2.0,
        "price_change_threshold": 0.05
    }
}
