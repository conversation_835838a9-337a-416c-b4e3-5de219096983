"""
Stock-related API endpoints.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from ..database import get_db
from ..models.user import User
from ..models.stock import Stock
from ..core.security import get_current_active_user
from ..utils.logger import log_api_call

router = APIRouter()


@router.get("/")
async def get_stocks(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    sector: Optional[str] = None,
    search: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get list of stocks with optional filtering."""
    log_api_call("/stocks", "GET", user_id=current_user.id)
    
    query = db.query(Stock).filter(Stock.is_active == True)
    
    if sector:
        query = query.filter(Stock.sector == sector)
    
    if search:
        query = query.filter(
            (Stock.symbol.ilike(f"%{search}%")) |
            (Stock.name.ilike(f"%{search}%"))
        )
    
    stocks = query.offset(skip).limit(limit).all()
    return [stock.to_dict() for stock in stocks]


@router.get("/{symbol}")
async def get_stock_detail(
    symbol: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get detailed information for a specific stock."""
    log_api_call(f"/stocks/{symbol}", "GET", user_id=current_user.id)
    
    stock = db.query(Stock).filter(Stock.symbol == symbol.upper()).first()
    if not stock:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Stock not found"
        )
    
    return stock.to_dict()


@router.get("/{symbol}/history")
async def get_stock_history(
    symbol: str,
    timeframe: str = Query("1d", regex="^(1m|5m|15m|1h|1d)$"),
    limit: int = Query(100, ge=1, le=1000),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get historical price data for a stock."""
    log_api_call(f"/stocks/{symbol}/history", "GET", user_id=current_user.id)
    
    stock = db.query(Stock).filter(Stock.symbol == symbol.upper()).first()
    if not stock:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Stock not found"
        )
    
    # This would typically fetch from price_history table
    # For now, return placeholder data
    return {
        "symbol": symbol.upper(),
        "timeframe": timeframe,
        "data": []
    }


@router.post("/screen")
async def screen_stocks(
    criteria: dict,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Screen stocks based on criteria."""
    log_api_call("/stocks/screen", "POST", user_id=current_user.id)
    
    # This would implement the screening logic
    # For now, return placeholder
    return {
        "criteria": criteria,
        "results": [],
        "total_matches": 0
    }
