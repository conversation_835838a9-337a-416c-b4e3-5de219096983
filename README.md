# EntryAlert - US Stock Screener with Watchlist Notifications

## 🚀 Overview

EntryAlert is a comprehensive US stock screener application that processes 8000+ stocks in real-time, providing intelligent watchlist notifications and entry point alerts. Built with modern technologies and custom algorithms for optimal performance.

## 🏗️ System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[React/TypeScript App]
        B[WebSocket Client]
        C[Chart Components]
        D[Notification Center]
    end
    
    subgraph "Backend Layer"
        E[FastAPI Server]
        F[WebSocket Handler]
        G[Authentication Service]
        H[Background Jobs]
    end
    
    subgraph "Business Logic"
        I[Stock Screener Engine]
        J[Technical Indicators]
        K[Alert Generator]
        L[Watchlist Manager]
    end
    
    subgraph "Data Layer"
        M[PostgreSQL Database]
        N[Redis Cache]
        O[Market Data Store]
    end
    
    subgraph "External APIs"
        P[Alpha Vantage API]
        Q[Yahoo Finance API]
        R[IEX Cloud API]
    end
    
    A --> E
    B --> F
    E --> G
    E --> I
    F --> K
    H --> I
    I --> J
    I --> L
    E --> M
    E --> N
    I --> O
    H --> P
    H --> Q
    H --> R
    
    style A fill:#e1f5fe
    style E fill:#f3e5f5
    style I fill:#e8f5e8
    style M fill:#fff3e0
```

## 🔄 Application Workflow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as API Server
    participant S as Screener Engine
    participant D as Database
    participant E as External APIs
    participant N as Notification Service
    
    U->>F: Login/Register
    F->>A: Authentication Request
    A->>D: Validate Credentials
    D-->>A: User Data
    A-->>F: JWT Token
    
    U->>F: Create Watchlist
    F->>A: POST /watchlists
    A->>D: Store Watchlist
    D-->>A: Confirmation
    A-->>F: Success Response
    
    Note over S,E: Background Process
    S->>E: Fetch Market Data
    E-->>S: Stock Prices & Volume
    S->>S: Run Screening Algorithms
    S->>D: Update Stock Data
    S->>N: Generate Alerts
    
    N->>F: WebSocket Alert
    F->>U: Real-time Notification
    
    U->>F: View Stock Details
    F->>A: GET /stocks/{symbol}
    A->>D: Query Stock Data
    D-->>A: Stock Information
    A-->>F: Stock Details
    F->>U: Display Charts & Data
```

## 📁 Project Structure

```
EntryAlert/
├── backend/                    # FastAPI Backend Application
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py            # FastAPI application entry point
│   │   ├── config.py          # Configuration settings
│   │   ├── database.py        # Database connection and setup
│   │   ├── models/            # SQLAlchemy models
│   │   │   ├── __init__.py
│   │   │   ├── user.py        # User model
│   │   │   ├── stock.py       # Stock model
│   │   │   ├── watchlist.py   # Watchlist model
│   │   │   └── alert.py       # Alert model
│   │   ├── schemas/           # Pydantic schemas
│   │   │   ├── __init__.py
│   │   │   ├── user.py
│   │   │   ├── stock.py
│   │   │   ├── watchlist.py
│   │   │   └── alert.py
│   │   ├── api/               # API routes
│   │   │   ├── __init__.py
│   │   │   ├── auth.py        # Authentication endpoints
│   │   │   ├── stocks.py      # Stock-related endpoints
│   │   │   ├── watchlists.py  # Watchlist endpoints
│   │   │   └── alerts.py      # Alert endpoints
│   │   ├── services/          # Business logic services
│   │   │   ├── __init__.py
│   │   │   ├── auth_service.py
│   │   │   ├── stock_service.py
│   │   │   ├── screener_service.py
│   │   │   ├── alert_service.py
│   │   │   └── market_data_service.py
│   │   ├── core/              # Core utilities
│   │   │   ├── __init__.py
│   │   │   ├── security.py    # JWT and password handling
│   │   │   ├── websocket.py   # WebSocket manager
│   │   │   └── background_tasks.py
│   │   ├── algorithms/        # Custom trading algorithms
│   │   │   ├── __init__.py
│   │   │   ├── technical_indicators.py
│   │   │   ├── screening_algorithms.py
│   │   │   └── entry_point_detector.py
│   │   └── utils/             # Utility functions
│   │       ├── __init__.py
│   │       ├── logger.py
│   │       └── helpers.py
│   ├── tests/                 # Backend tests
│   ├── requirements.txt       # Python dependencies
│   ├── .env.example          # Environment variables template
│   └── alembic/              # Database migrations
├── frontend/                  # React Frontend Application
│   ├── public/
│   │   ├── index.html
│   │   └── favicon.ico
│   ├── src/
│   │   ├── components/        # Reusable components
│   │   │   ├── common/
│   │   │   ├── charts/
│   │   │   ├── forms/
│   │   │   └── layout/
│   │   ├── pages/             # Page components
│   │   │   ├── Dashboard.tsx
│   │   │   ├── Screener.tsx
│   │   │   ├── Watchlist.tsx
│   │   │   ├── StockDetail.tsx
│   │   │   └── Settings.tsx
│   │   ├── hooks/             # Custom React hooks
│   │   ├── services/          # API service functions
│   │   ├── store/             # State management
│   │   ├── types/             # TypeScript type definitions
│   │   ├── utils/             # Utility functions
│   │   ├── App.tsx
│   │   └── index.tsx
│   ├── package.json
│   ├── tsconfig.json
│   └── tailwind.config.js
├── docs/                      # Documentation
├── docker-compose.yml         # Docker configuration
├── .gitignore
└── README.md
```

## 🛠️ Technology Stack

### Backend
- **Framework**: FastAPI (Python 3.9+)
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Caching**: Redis for market data caching
- **Authentication**: JWT tokens
- **WebSocket**: FastAPI WebSocket support
- **Background Jobs**: APScheduler for periodic tasks
- **Market Data**: Alpha Vantage, Yahoo Finance APIs

### Frontend
- **Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS
- **Charts**: Chart.js / Recharts
- **State Management**: React Query + Context API
- **WebSocket**: Native WebSocket API
- **Build Tool**: Vite

### DevOps & Tools
- **Containerization**: Docker & Docker Compose
- **Database Migrations**: Alembic
- **Testing**: Pytest (Backend), Jest (Frontend)
- **Code Quality**: ESLint, Prettier, Black
- **CI/CD**: GitHub Actions

## 🚀 Quick Start

### Prerequisites
- Python 3.9+
- Node.js 16+
- PostgreSQL 13+
- Redis 6+

### Backend Setup
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
cp .env.example .env
# Configure your environment variables
alembic upgrade head
uvicorn app.main:app --reload
```

### Frontend Setup
```bash
cd frontend
npm install
npm run dev
```

### Docker Setup
```bash
docker-compose up -d
```

## 📊 Features

### Core Features
- ✅ Real-time stock screening across 8000+ US stocks
- ✅ Custom technical indicators (RSI, MACD, Bollinger Bands)
- ✅ Intelligent watchlist management
- ✅ Entry point detection algorithms
- ✅ Multi-channel notifications (email, push)
- ✅ Industry sector analysis
- ✅ WebSocket real-time updates

### Advanced Features
- 🔄 Custom screening algorithms
- 📈 Support/resistance level detection
- 📊 Volume surge analysis
- 🎯 Momentum change alerts
- 📱 Mobile-responsive design
- 🔐 Secure JWT authentication

## 🔧 Configuration

### Environment Variables
```env
# Database
DATABASE_URL=postgresql://user:password@localhost/entryalert
REDIS_URL=redis://localhost:6379

# API Keys
ALPHA_VANTAGE_API_KEY=your_api_key
YAHOO_FINANCE_API_KEY=your_api_key

# Security
SECRET_KEY=your_secret_key
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=your_email
SMTP_PASSWORD=your_password
```

## 📈 API Documentation

Once the backend is running, visit:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## 🧪 Testing

### Backend Tests
```bash
cd backend
pytest
```

### Frontend Tests
```bash
cd frontend
npm test
```

## 🚀 Deployment

### Production Deployment
1. Configure production environment variables
2. Build frontend: `npm run build`
3. Deploy using Docker: `docker-compose -f docker-compose.prod.yml up -d`

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🤝 Support

For support and questions, please open an issue on GitHub.

---

**Built with ❤️ for the trading community**
